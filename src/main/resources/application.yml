mp:
  openapi:
    extensions:
      smallrye:
        info:
          title: Authentication API
          version: 1.0.0
application:
  keycloakServerUri: ${KEYCLOAK_SERVER_URL:https://identity-stage.glideyoke.com}
  defaultTenantId: ${DEFAULT_TENANT_ID:master}
  caching:
    enabled: ${CACHING_ENABLED:true}
    # Cache TTL for company validation (5 minutes)
    company-validation-ttl: ${COMPANY_VALIDATION_CACHE_TTL:PT5M}
    # Cache TTL for role lookups (10 minutes)
    role-lookup-ttl: ${ROLE_LOOKUP_CACHE_TTL:PT10M}
    # Cache TTL for group information (5 minutes)
    group-info-ttl: ${GROUP_INFO_CACHE_TTL:PT5M}
  triggerVerifyEmail: ${TRIGGER_VERIFY_EMAIL:false}
  triggerNotifyEmail: ${TRIGGER_NOTIFY_EMAIL:false}
  # Async processing configuration
  async:
    # Enable async post-creation tasks (email verification, notifications)
    post-creation-tasks: ${ASYNC_POST_CREATION_TASKS:true}
    # Thread pool size for async operations
    thread-pool-size: ${ASYNC_THREAD_POOL_SIZE:10}
    # Queue capacity for async operations
    queue-capacity: ${ASYNC_QUEUE_CAPACITY:100}
db:
  addresses: ${DB_SERVER:bolt://localhost:7687}
  database: ${DB_NAME:neo4j}
  type: ${DB_TYPE:neo4j}
  username: ${DB_USERNAME:neo4j}
  password: ${DB_PASSWORD:neo4j}
  query:
    defaultSize: 10
quarkus:
  app:
    name: auth-service
  oidc:
    authentication:
      cookie-domain: ${COOKIE_DOMAIN:glideyoke.com}
    internalClientSecret: ${INTERNAL_CLIENT_SECRET:MGLxMiU13MBtO8dQPUFMaLaO7ZV3ql9a}
    # oAuth login for internal communication
    internalClientId: ${INTERNAL_CLIENT_ID:glide-service-admin}
  http:
    timeout: ${HTTP_TIMEOUT:30s}
    port: ${QUARKUS_PORT:8080}
    cors:
      ~: true
      origins: "*"
    # Connection pool settings for better performance
    connection-pool-size: ${HTTP_CONNECTION_POOL_SIZE:50}
    connection-timeout: ${HTTP_CONNECTION_TIMEOUT:10s}
    read-timeout: ${HTTP_READ_TIMEOUT:30s}
  smallrye-openapi:
    path: /api/q/openapi
    security-scheme: jwt
    security-scheme-name: apiToken
  swagger-ui:
    always-include: true
    theme: material
    path: /api
  datasource:
    db-kind: postgresql
    jdbc: false
    reactive:
      max-size: ${DB_CONNECTION_POOL_SIZE:32}
      initial-size: ${DB_CONNECTION_POOL_INITIAL_SIZE:8}
      idle-timeout: ${DB_CONNECTION_IDLE_TIMEOUT:PT10M}
      max-lifetime: ${DB_CONNECTION_MAX_LIFETIME:PT30M}
      url: ${DB_AUTH_URL:vertx-reactive:postgresql://localhost:35432/auth_db}
    username: ${DB_AUTH_USERNAME:auth}
    password: ${DB_AUTH_PASSWORD:z8swrUXftp3Zs9kBB5^sjH}
  hibernate-orm:
    database:
      generation: none
    log:
      sql: true
  kafka:
    devservices:
      enabled: false
  otel:
    traces:
      exporter: none
  redis:
    hosts: ${REDIS_URL:redis://localhost:6379}
    timeout: ${REDIS_TIMEOUT:10s}
    max-pool-size: ${REDIS_MAX_POOL_SIZE:20}
    max-pool-waiting: ${REDIS_MAX_POOL_WAITING:24}

# client config
entity-api/mp-rest/uri: ${ENTITY_SERVICE_URL:http://localhost:8888}
entity-api/mp-rest/connectTimeout: ${ENTITY_SERVICE_CONNECT_TIMEOUT:5000}
entity-api/mp-rest/readTimeout: ${ENTITY_SERVICE_READ_TIMEOUT:30000}
entity-api/mp-rest/connectionPoolSize: ${ENTITY_SERVICE_CONNECTION_POOL_SIZE:20}

security:
  keycloak:
    superAdminUsername: superadmin
    superAdminPassword: ${SUPER_ADMIN_PASSWORD:UXWnVXxQrLbYBsKNxNmz}
    systemClientsToPersistForNewRealm:
      - sp-services-internal # TODO: Remove after all services have been upgraded
      - ${INTERNAL_CLIENT_ID:glide-service-admin}
      - sp-services
    systemClients:
      - sp-services
    custom-smtp:
      host: ${CUSTOM_SMTP_HOST:}
      port: ${CUSTOM_SMTP_PORT:}
      username: ${CUSTOM_SMTP_USERNAME:}
      password: ${CUSTOM_SMTP_PASSWORD:}