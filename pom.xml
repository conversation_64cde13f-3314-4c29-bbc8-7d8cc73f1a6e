<?xml version="1.0"?>
<!--
  ~ Copyright (c) 2023-2025 Glide Systems, Inc.
  ~ 19925 Stevens Creek Blvd, Cupertino, CA 95014
  ~ All rights reserved.
  ~ This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
  ~ (the "License Agreement"). The Software is the confidential and proprietary information
  ~ of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
  ~ THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
  ~ MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
  ~ BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
  ~ MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
  -->

<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.tripudiotech</groupId>
  <artifactId>auth-mservice</artifactId>
  <version>3.1.0</version>
  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <compiler-plugin.version>3.13.0</compiler-plugin.version>
    <maven.compiler.exchangeParameters>true</maven.compiler.exchangeParameters>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <quarkus.platform.artifact-id>quarkus-universe-bom</quarkus.platform.artifact-id>
    <quarkus.platform.group-id>io.quarkus</quarkus.platform.group-id>
    <quarkus.platform.version>3.13.2</quarkus.platform.version>
    <surefire-plugin.version>3.2.5</surefire-plugin.version>
    <base-service.version>4.2.3</base-service.version>
    <security-lib.version>2.6.3</security-lib.version>
  </properties>
  <repositories>
    <repository>
        <id>maven-repo.tripudiotech.com</id>
        <url>s3://maven-repo.tripudiotech.com/release</url>
    </repository>
  </repositories>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${quarkus.platform.group-id}</groupId>
        <artifactId>${quarkus.platform.artifact-id}</artifactId>
        <version>${quarkus.platform.version}</version>
        <type>pom</type>
        <scope>import</scope>
        <exclusions>
          <exclusion>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>com.tripudiotech</groupId>
      <artifactId>base-mservice</artifactId>
      <version>${base-service.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.avro</groupId>
          <artifactId>avro</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.confluent</groupId>
          <artifactId>kafka-avro-serializer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.quarkus</groupId>
          <artifactId>quarkus-redis-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.quarkus</groupId>
          <artifactId>quarkus-confluent-registry-avro</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.tripudiotech</groupId>
      <artifactId>security-lib</artifactId>
      <version>${security-lib.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.keycloak</groupId>
          <artifactId>keycloak-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Hibernate Reactive dependency -->
    <dependency>
      <groupId>io.quarkus</groupId>
      <artifactId>quarkus-hibernate-reactive-panache</artifactId>
    </dependency>

    <!-- Reactive SQL client for PostgreSQL -->
    <dependency>
      <groupId>io.quarkus</groupId>
      <artifactId>quarkus-reactive-pg-client</artifactId>
    </dependency>

    <!-- Redis client for caching -->
    <dependency>
      <groupId>io.quarkus</groupId>
      <artifactId>quarkus-redis-client</artifactId>
    </dependency>
  </dependencies>


  <build>
    <extensions>
      <extension>
        <groupId>com.allogy.maven.wagon</groupId>
        <artifactId>maven-s3-wagon</artifactId>
        <version>1.2.0</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <groupId>${quarkus.platform.group-id}</groupId>
        <artifactId>quarkus-maven-plugin</artifactId>
        <version>${quarkus.platform.version}</version>
        <extensions>true</extensions>
        <executions>
          <execution>
            <goals>
              <goal>build</goal>
              <goal>generate-code</goal>
              <goal>generate-code-tests</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
